is_global = true
build_property.TargetFramework = net9.0
build_property.TargetFramework = net9.0
build_property.TargetPlatformMinVersion = 
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = true
build_property.UsingMicrosoftNETSdkWeb = true
build_property.ProjectTypeGuids = 
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = WebApplication1
build_property.RootNamespace = WebApplication1
build_property.ProjectDir = C:\Users\<USER>\source\repos\WebApplication1\WebApplication1\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.RazorLangVersion = 9.0
build_property.SupportLocalizedComponentNames = 
build_property.GenerateRazorMetadataSourceChecksumAttributes = 
build_property.MSBuildProjectDirectory = C:\Users\<USER>\source\repos\WebApplication1\WebApplication1
build_property._RazorSourceGeneratorDebug = 
build_property.EffectiveAnalysisLevelStyle = 9.0
build_property.EnableCodeStyleSeverity = 

[C:/Users/<USER>/source/repos/WebApplication1/WebApplication1/Views/Admin/Companies.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQWRtaW5cQ29tcGFuaWVzLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/WebApplication1/WebApplication1/Views/Admin/CreateCompany.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQWRtaW5cQ3JlYXRlQ29tcGFueS5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/WebApplication1/WebApplication1/Views/Admin/CreateManager.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQWRtaW5cQ3JlYXRlTWFuYWdlci5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/WebApplication1/WebApplication1/Views/Admin/EditCompany.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQWRtaW5cRWRpdENvbXBhbnkuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/WebApplication1/WebApplication1/Views/Admin/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQWRtaW5cSW5kZXguY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/WebApplication1/WebApplication1/Views/CompanyManager/CreateOffer.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQ29tcGFueU1hbmFnZXJcQ3JlYXRlT2ZmZXIuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/WebApplication1/WebApplication1/Views/CompanyManager/CreateOffice.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQ29tcGFueU1hbmFnZXJcQ3JlYXRlT2ZmaWNlLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/WebApplication1/WebApplication1/Views/CompanyManager/EditOffice.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQ29tcGFueU1hbmFnZXJcRWRpdE9mZmljZS5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/WebApplication1/WebApplication1/Views/CompanyManager/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQ29tcGFueU1hbmFnZXJcSW5kZXguY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/WebApplication1/WebApplication1/Views/CompanyManager/Offers.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQ29tcGFueU1hbmFnZXJcT2ZmZXJzLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/WebApplication1/WebApplication1/Views/CompanyManager/Offices.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQ29tcGFueU1hbmFnZXJcT2ZmaWNlcy5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/WebApplication1/WebApplication1/Views/Home/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcSG9tZVxJbmRleC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/WebApplication1/WebApplication1/Views/Home/Privacy.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcSG9tZVxQcml2YWN5LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/WebApplication1/WebApplication1/Views/Permission/ManageUserPermissions.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcUGVybWlzc2lvblxNYW5hZ2VVc2VyUGVybWlzc2lvbnMuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/WebApplication1/WebApplication1/Views/Shared/Error.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXEVycm9yLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/WebApplication1/WebApplication1/Views/Shared/_LoginPartial.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXF9Mb2dpblBhcnRpYWwuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/WebApplication1/WebApplication1/Views/Shared/_ValidationScriptsPartial.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXF9WYWxpZGF0aW9uU2NyaXB0c1BhcnRpYWwuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/WebApplication1/WebApplication1/Views/_ViewImports.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcX1ZpZXdJbXBvcnRzLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/WebApplication1/WebApplication1/Views/_ViewStart.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcX1ZpZXdTdGFydC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/WebApplication1/WebApplication1/Views/Shared/_Layout.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXF9MYXlvdXQuY3NodG1s
build_metadata.AdditionalFiles.CssScope = b-bddmc6yalx
