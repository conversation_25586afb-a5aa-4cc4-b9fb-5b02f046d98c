@model IEnumerable<WebApplication1.Models.InsuranceOffer>
@{
    ViewData["Title"] = "إدارة العروض";
    var offices = ViewBag.Offices as List<WebApplication1.Models.Office>;
    var selectedOfficeId = ViewBag.SelectedOfficeId as int?;
}

<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-6">
            <h2 class="text-primary">
                <i class="fas fa-tags"></i> إدارة العروض
            </h2>
        </div>
        <div class="col-md-6 text-end">
            <a href="@Url.Action("CreateOffer")" class="btn btn-success">
                <i class="fas fa-plus"></i> إضافة عرض جديد
            </a>
        </div>
    </div>

    <!-- Filter by Office -->
    @if (offices != null && offices.Any())
    {
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <form method="get" asp-action="Offers">
                            <div class="row align-items-end">
                                <div class="col-md-4">
                                    <label class="form-label">تصفية حسب المكتب:</label>
                                    <select name="officeId" class="form-select">
                                        <option value="">جميع المكاتب</option>
                                        @foreach (var office in offices)
                                        {
                                            <option value="@office.Id" selected="@(selectedOfficeId == office.Id)">
                                                @office.Name
                                            </option>
                                        }
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-filter"></i> تصفية
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    }

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    @if (Model.Any())
                    {
                        <div class="row">
                            @foreach (var offer in Model)
                            {
                                <div class="col-lg-6 col-xl-4 mb-4">
                                    <div class="card h-100 border-left-primary">
                                        <div class="card-header">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <h6 class="text-primary mb-0">@offer.Title</h6>
                                                @if (offer.IsActive)
                                                {
                                                    <span class="badge bg-success">نشط</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-secondary">غير نشط</span>
                                                }
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <p class="card-text">@offer.Description</p>
                                            
                                            <div class="mb-2">
                                                <small class="text-muted">
                                                    <i class="fas fa-building"></i> @offer.Office.Name
                                                </small>
                                            </div>

                                            @if (!string.IsNullOrEmpty(offer.Coverage))
                                            {
                                                <div class="mb-2">
                                                    <span class="badge bg-info">@offer.Coverage</span>
                                                </div>
                                            }

                                            @if (offer.MinPrice.HasValue || offer.MaxPrice.HasValue)
                                            {
                                                <div class="mb-2">
                                                    <strong class="text-success">
                                                        @if (offer.MinPrice.HasValue && offer.MaxPrice.HasValue)
                                                        {
                                                            @($"{offer.MinPrice:N0} - {offer.MaxPrice:N0} {offer.Currency}")
                                                        }
                                                        else if (offer.MinPrice.HasValue)
                                                        {
                                                            @($"من {offer.MinPrice:N0} {offer.Currency}")
                                                        }
                                                        else if (offer.MaxPrice.HasValue)
                                                        {
                                                            @($"حتى {offer.MaxPrice:N0} {offer.Currency}")
                                                        }
                                                    </strong>
                                                </div>
                                            }

                                            @if (!string.IsNullOrEmpty(offer.Terms))
                                            {
                                                <div class="mb-2">
                                                    <small class="text-muted">
                                                        <strong>الشروط:</strong> @offer.Terms
                                                    </small>
                                                </div>
                                            }

                                            <div class="text-muted">
                                                <small>تاريخ الإنشاء: @offer.CreatedAt.ToString("dd/MM/yyyy")</small>
                                            </div>
                                        </div>
                                        <div class="card-footer bg-white">
                                            <div class="btn-group w-100" role="group">
                                                <button type="button" class="btn btn-outline-primary btn-sm">
                                                    <i class="fas fa-edit"></i> تعديل
                                                </button>
                                                <button type="button" class="btn btn-outline-danger btn-sm" 
                                                        onclick="if(confirm('هل أنت متأكد من حذف هذا العرض؟')) { /* Add delete logic */ }">
                                                    <i class="fas fa-trash"></i> حذف
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                            <h4 class="text-muted">لا توجد عروض</h4>
                            @if (selectedOfficeId.HasValue)
                            {
                                <p class="text-muted">لا توجد عروض في المكتب المحدد</p>
                                <a href="@Url.Action("Offers")" class="btn btn-secondary me-2">
                                    عرض جميع العروض
                                </a>
                            }
                            else
                            {
                                <p class="text-muted">ابدأ بإضافة عرض جديد</p>
                            }
                            <a href="@Url.Action("CreateOffer")" class="btn btn-primary">
                                <i class="fas fa-plus"></i> إضافة عرض جديد
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>
