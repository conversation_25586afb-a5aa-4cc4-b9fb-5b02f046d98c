using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using WebApplication1.Data;
using WebApplication1.Models;
using WebApplication1.ViewModels;

namespace WebApplication1.Controllers
{
    [Authorize(Roles = "CompanyManager")]
    public class CompanyManagerController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly UserManager<User> _userManager;

        public CompanyManagerController(ApplicationDbContext context, UserManager<User> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        public async Task<IActionResult> Index()
        {
            var user = await _userManager.GetUserAsync(User);
            if (user?.CompanyId == null)
            {
                return Forbid();
            }

            var company = await _context.InsuranceCompanies
                .Include(c => c.Offices)
                .ThenInclude(o => o.Offers)
                .FirstOrDefaultAsync(c => c.Id == user.CompanyId);

            if (company == null)
            {
                return NotFound();
            }

            var stats = new CompanyDashboardViewModel
            {
                Company = company,
                TotalOffices = company.Offices.Count,
                TotalOffers = company.Offices.SelectMany(o => o.Offers).Count(),
                ActiveOffices = company.Offices.Count(o => o.IsActive),
                ActiveOffers = company.Offices.SelectMany(o => o.Offers).Count(o => o.IsActive)
            };

            return View(stats);
        }

        // Offices Management
        public async Task<IActionResult> Offices()
        {
            var user = await _userManager.GetUserAsync(User);
            if (user?.CompanyId == null)
            {
                return Forbid();
            }

            var offices = await _context.Offices
                .Include(o => o.Offers)
                .Where(o => o.CompanyId == user.CompanyId)
                .ToListAsync();

            return View(offices);
        }

        public async Task<IActionResult> CreateOffice()
        {
            var user = await _userManager.GetUserAsync(User);
            if (user?.CompanyId == null)
            {
                return Forbid();
            }

            ViewBag.CompanyId = user.CompanyId;
            return View();
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateOffice(Office office)
        {
            var user = await _userManager.GetUserAsync(User);
            if (user?.CompanyId == null)
            {
                return Forbid();
            }

            office.CompanyId = user.CompanyId.Value;

            if (ModelState.IsValid)
            {
                _context.Offices.Add(office);
                await _context.SaveChangesAsync();
                TempData["Success"] = "تم إنشاء المكتب بنجاح";
                return RedirectToAction(nameof(Offices));
            }

            ViewBag.CompanyId = user.CompanyId;
            return View(office);
        }

        public async Task<IActionResult> EditOffice(int id)
        {
            var user = await _userManager.GetUserAsync(User);
            if (user?.CompanyId == null)
            {
                return Forbid();
            }

            var office = await _context.Offices
                .FirstOrDefaultAsync(o => o.Id == id && o.CompanyId == user.CompanyId);

            if (office == null)
            {
                return NotFound();
            }

            return View(office);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> EditOffice(int id, Office office)
        {
            var user = await _userManager.GetUserAsync(User);
            if (user?.CompanyId == null)
            {
                return Forbid();
            }

            if (id != office.Id || office.CompanyId != user.CompanyId)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    _context.Update(office);
                    await _context.SaveChangesAsync();
                    TempData["Success"] = "تم تحديث المكتب بنجاح";
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!OfficeExists(office.Id))
                    {
                        return NotFound();
                    }
                    throw;
                }
                return RedirectToAction(nameof(Offices));
            }
            return View(office);
        }

        [HttpPost]
        public async Task<IActionResult> DeleteOffice(int id)
        {
            var user = await _userManager.GetUserAsync(User);
            if (user?.CompanyId == null)
            {
                return Forbid();
            }

            var office = await _context.Offices
                .FirstOrDefaultAsync(o => o.Id == id && o.CompanyId == user.CompanyId);

            if (office != null)
            {
                _context.Offices.Remove(office);
                await _context.SaveChangesAsync();
                TempData["Success"] = "تم حذف المكتب بنجاح";
            }

            return RedirectToAction(nameof(Offices));
        }

        // Offers Management
        public async Task<IActionResult> Offers(int? officeId)
        {
            var user = await _userManager.GetUserAsync(User);
            if (user?.CompanyId == null)
            {
                return Forbid();
            }

            var query = _context.InsuranceOffers
                .Include(o => o.Office)
                .Where(o => o.Office.CompanyId == user.CompanyId);

            if (officeId.HasValue)
            {
                query = query.Where(o => o.OfficeId == officeId.Value);
            }

            var offers = await query.ToListAsync();
            var offices = await _context.Offices
                .Where(o => o.CompanyId == user.CompanyId && o.IsActive)
                .ToListAsync();

            ViewBag.Offices = offices;
            ViewBag.SelectedOfficeId = officeId;

            return View(offers);
        }

        public async Task<IActionResult> CreateOffer()
        {
            var user = await _userManager.GetUserAsync(User);
            if (user?.CompanyId == null)
            {
                return Forbid();
            }

            var offices = await _context.Offices
                .Where(o => o.CompanyId == user.CompanyId && o.IsActive)
                .ToListAsync();

            ViewBag.Offices = offices;
            return View();
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateOffer(InsuranceOffer offer)
        {
            var user = await _userManager.GetUserAsync(User);
            if (user?.CompanyId == null)
            {
                return Forbid();
            }

            // Verify that the office belongs to the user's company
            var office = await _context.Offices
                .FirstOrDefaultAsync(o => o.Id == offer.OfficeId && o.CompanyId == user.CompanyId);

            if (office == null)
            {
                ModelState.AddModelError("OfficeId", "المكتب المحدد غير صحيح");
            }

            if (ModelState.IsValid)
            {
                _context.InsuranceOffers.Add(offer);
                await _context.SaveChangesAsync();
                TempData["Success"] = "تم إنشاء العرض بنجاح";
                return RedirectToAction(nameof(Offers));
            }

            var offices = await _context.Offices
                .Where(o => o.CompanyId == user.CompanyId && o.IsActive)
                .ToListAsync();

            ViewBag.Offices = offices;
            return View(offer);
        }

        private bool OfficeExists(int id)
        {
            return _context.Offices.Any(e => e.Id == id);
        }
    }
}
