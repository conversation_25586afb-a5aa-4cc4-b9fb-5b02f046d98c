﻿@model IEnumerable<WebApplication1.Models.InsuranceCompany>
@{
    ViewData["Title"] = "تأمين السيارات";
}

<div class="container-fluid">
    <!-- Hero Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-primary text-white border-0" style="background: linear-gradient(135deg, #0d6efd 0%, #0056b3 100%);">
                <div class="card-body text-center py-5">
                    <div class="fade-in">
                        <h1 class="display-4 mb-3 fw-bold">
                            <i class="fas fa-car me-3"></i>موقع تأمين السيارات
                        </h1>
                        <p class="lead mb-4">اكتشف أفضل عروض التأمين من شركات التأمين الرائدة في المملكة العربية السعودية</p>
                        <div class="row justify-content-center">
                            <div class="col-md-8">
                                <div class="d-flex justify-content-center gap-4 text-center">
                                    <div>
                                        <i class="fas fa-shield-alt fa-2x mb-2"></i>
                                        <p class="mb-0 small">حماية شاملة</p>
                                    </div>
                                    <div>
                                        <i class="fas fa-clock fa-2x mb-2"></i>
                                        <p class="mb-0 small">خدمة سريعة</p>
                                    </div>
                                    <div>
                                        <i class="fas fa-star fa-2x mb-2"></i>
                                        <p class="mb-0 small">جودة عالية</p>
                                    </div>
                                    <div>
                                        <i class="fas fa-handshake fa-2x mb-2"></i>
                                        <p class="mb-0 small">ثقة وأمان</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-body">
                    <form method="get" asp-action="Index">
                        <div class="row">
                            <div class="col-md-10">
                                <input type="text" name="searchTerm" value="@ViewBag.SearchTerm"
                                       class="form-control form-control-lg"
                                       placeholder="ابحث عن شركة التأمين..." />
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-primary btn-lg w-100">
                                    <i class="fas fa-search"></i> بحث
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    @if (Model.Any())
    {
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-3 col-6 mb-3">
                                <div class="d-flex flex-column align-items-center">
                                    <i class="fas fa-building fa-2x text-primary mb-2"></i>
                                    <h4 class="mb-1 text-primary">@Model.Count()</h4>
                                    <small class="text-muted">شركة تأمين</small>
                                </div>
                            </div>
                            <div class="col-md-3 col-6 mb-3">
                                <div class="d-flex flex-column align-items-center">
                                    <i class="fas fa-map-marker-alt fa-2x text-success mb-2"></i>
                                    <h4 class="mb-1 text-success">@Model.SelectMany(c => c.Offices).Count(o => o.IsActive)</h4>
                                    <small class="text-muted">مكتب نشط</small>
                                </div>
                            </div>
                            <div class="col-md-3 col-6 mb-3">
                                <div class="d-flex flex-column align-items-center">
                                    <i class="fas fa-tags fa-2x text-info mb-2"></i>
                                    <h4 class="mb-1 text-info">@Model.SelectMany(c => c.Offices).SelectMany(o => o.Offers).Count(o => o.IsActive)</h4>
                                    <small class="text-muted">عرض متاح</small>
                                </div>
                            </div>
                            <div class="col-md-3 col-6 mb-3">
                                <div class="d-flex flex-column align-items-center">
                                    <i class="fas fa-city fa-2x text-warning mb-2"></i>
                                    <h4 class="mb-1 text-warning">@Model.SelectMany(c => c.Offices).Where(o => !string.IsNullOrEmpty(o.City)).Select(o => o.City).Distinct().Count()</h4>
                                    <small class="text-muted">مدينة</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }

    <!-- Companies Section -->
    <div class="row">
        @if (Model.Any())
        {
            @foreach (var company in Model)
            {
                <div class="col-lg-6 col-xl-4 mb-4">
                    <div class="card h-100 shadow-sm border-0 fade-in">
                        <div class="card-header bg-gradient" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0 text-primary fw-bold">
                                    <i class="fas fa-building me-2"></i>@company.Name
                                </h5>
                                <span class="badge bg-success">نشط</span>
                            </div>
                        </div>
                        <div class="card-body">
                            @if (!string.IsNullOrEmpty(company.Description))
                            {
                                <p class="card-text text-muted">@company.Description</p>
                            }

                            <div class="row mb-3">
                                @if (!string.IsNullOrEmpty(company.Phone))
                                {
                                    <div class="col-12 mb-2">
                                        <small class="text-muted">
                                            <i class="fas fa-phone"></i> @company.Phone
                                        </small>
                                    </div>
                                }
                                @if (!string.IsNullOrEmpty(company.Email))
                                {
                                    <div class="col-12 mb-2">
                                        <small class="text-muted">
                                            <i class="fas fa-envelope"></i> @company.Email
                                        </small>
                                    </div>
                                }
                                @if (!string.IsNullOrEmpty(company.Website))
                                {
                                    <div class="col-12 mb-2">
                                        <small class="text-muted">
                                            <i class="fas fa-globe"></i>
                                            <a href="@company.Website" target="_blank">@company.Website</a>
                                        </small>
                                    </div>
                                }
                            </div>

                            <div class="mb-3">
                                <h6 class="text-secondary">
                                    <i class="fas fa-map-marker-alt"></i> المكاتب (@company.Offices.Count(o => o.IsActive))
                                </h6>
                            </div>
                        </div>
                        <div class="card-footer bg-white">
                            <button class="btn btn-outline-primary btn-sm" type="button"
                                    data-bs-toggle="collapse"
                                    data-bs-target="#<EMAIL>"
                                    aria-expanded="false">
                                <i class="fas fa-eye"></i> عرض المكاتب والعروض
                            </button>
                        </div>

                        <!-- Collapsible Offices Section -->
                        <div class="collapse" id="<EMAIL>">
                            <div class="card-body border-top">
                                @foreach (var office in company.Offices.Where(o => o.IsActive))
                                {
                                    <div class="border rounded p-3 mb-3 bg-light">
                                        <h6 class="text-primary mb-2">
                                            <i class="fas fa-building"></i> @office.Name
                                        </h6>
                                        <p class="small mb-2">
                                            <i class="fas fa-map-marker-alt"></i> @office.Address
                                        </p>
                                        <p class="small mb-2">
                                            <i class="fas fa-phone"></i>
                                            <a href="tel:@office.Phone" class="text-decoration-none">@office.Phone</a>
                                        </p>
                                        @if (!string.IsNullOrEmpty(office.Description))
                                        {
                                            <p class="small text-muted mb-3">@office.Description</p>
                                        }

                                        <!-- Office Offers -->
                                        @if (office.Offers.Any(o => o.IsActive))
                                        {
                                            <div class="mt-3">
                                                <h6 class="text-success mb-2">
                                                    <i class="fas fa-tags"></i> العروض المتاحة:
                                                </h6>
                                                @foreach (var offer in office.Offers.Where(o => o.IsActive))
                                                {
                                                    <div class="border rounded p-2 mb-2 bg-white">
                                                        <div class="d-flex justify-content-between align-items-start">
                                                            <div>
                                                                <strong class="text-primary">@offer.Title</strong>
                                                                <p class="small mb-1">@offer.Description</p>
                                                                @if (!string.IsNullOrEmpty(offer.Coverage))
                                                                {
                                                                    <span class="badge bg-info">@offer.Coverage</span>
                                                                }
                                                            </div>
                                                            @if (offer.MinPrice.HasValue || offer.MaxPrice.HasValue)
                                                            {
                                                                <div class="text-end">
                                                                    <small class="text-success fw-bold">
                                                                        @if (offer.MinPrice.HasValue && offer.MaxPrice.HasValue)
                                                                        {
                                                                            @($"{offer.MinPrice:N0} - {offer.MaxPrice:N0} {offer.Currency}")
                                                                        }
                                                                        else if (offer.MinPrice.HasValue)
                                                                        {
                                                                            @($"من {offer.MinPrice:N0} {offer.Currency}")
                                                                        }
                                                                        else if (offer.MaxPrice.HasValue)
                                                                        {
                                                                            @($"حتى {offer.MaxPrice:N0} {offer.Currency}")
                                                                        }
                                                                    </small>
                                                                </div>
                                                            }
                                                        </div>
                                                    </div>
                                                }
                                            </div>
                                        }
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            }
        }
        else
        {
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">لا توجد شركات تأمين متاحة</h4>
                    @if (!string.IsNullOrEmpty(ViewBag.SearchTerm))
                    {
                        <p class="text-muted">لم يتم العثور على نتائج للبحث: "@ViewBag.SearchTerm"</p>
                        <a href="@Url.Action("Index")" class="btn btn-primary">عرض جميع الشركات</a>
                    }
                </div>
            </div>
        }
    </div>
</div>
