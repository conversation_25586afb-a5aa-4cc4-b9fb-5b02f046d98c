using System.ComponentModel.DataAnnotations;

namespace WebApplication1.Models
{
    public class Office
    {
        public int Id { get; set; }

        [Required]
        [StringLength(200)]
        public string Name { get; set; } = string.Empty;

        [Required]
        [StringLength(500)]
        public string Address { get; set; } = string.Empty;

        [Required]
        [StringLength(20)]
        public string Phone { get; set; } = string.Empty;

        [StringLength(1000)]
        public string? Description { get; set; }

        [StringLength(100)]
        public string? City { get; set; }

        [StringLength(100)]
        public string? Region { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Foreign key
        [Required]
        public int CompanyId { get; set; }

        // Navigation properties
        public virtual InsuranceCompany Company { get; set; } = null!;
        public virtual ICollection<InsuranceOffer> Offers { get; set; } = new List<InsuranceOffer>();
    }
}
