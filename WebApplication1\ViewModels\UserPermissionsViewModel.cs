using WebApplication1.Models;

namespace WebApplication1.ViewModels
{
    public class UserPermissionsViewModel
    {
        public User User { get; set; } = null!;
        public List<Permission> UserPermissions { get; set; } = new List<Permission>();
        public List<Permission> AllPermissions { get; set; } = new List<Permission>();

        public bool HasPermission(string permissionName)
        {
            return UserPermissions.Any(p => p.Name == permissionName);
        }

        public List<Permission> GetPermissionsByCategory(string category)
        {
            return AllPermissions.Where(p => p.Category == category).ToList();
        }

        public List<Permission> GetAvailablePermissions()
        {
            return AllPermissions.Where(p => !UserPermissions.Any(up => up.Id == p.Id)).ToList();
        }
    }

    public class CreateUserWithPermissionsViewModel
    {
        public CreateManagerViewModel UserInfo { get; set; } = new CreateManagerViewModel();
        public List<string> SelectedPermissions { get; set; } = new List<string>();
        public List<Permission> AvailablePermissions { get; set; } = new List<Permission>();
    }
}
