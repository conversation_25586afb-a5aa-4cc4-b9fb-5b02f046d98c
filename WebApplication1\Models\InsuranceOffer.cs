using System.ComponentModel.DataAnnotations;

namespace WebApplication1.Models
{
    public class InsuranceOffer
    {
        public int Id { get; set; }

        [Required]
        [StringLength(200)]
        public string Title { get; set; } = string.Empty;

        [Required]
        [StringLength(2000)]
        public string Description { get; set; } = string.Empty;

        [StringLength(100)]
        public string? Coverage { get; set; }

        [Range(0, double.MaxValue)]
        public decimal? MinPrice { get; set; }

        [Range(0, double.MaxValue)]
        public decimal? MaxPrice { get; set; }

        [StringLength(50)]
        public string? Currency { get; set; } = "ريال سعودي";

        [StringLength(1000)]
        public string? Terms { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Foreign key
        [Required]
        public int OfficeId { get; set; }

        // Navigation property
        public virtual Office Office { get; set; } = null!;
    }
}
