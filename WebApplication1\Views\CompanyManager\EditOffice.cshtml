@model WebApplication1.Models.Office
@{
    ViewData["Title"] = "تعديل المكتب";
}

<div class="container">
    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-edit"></i> تعديل المكتب
                    </h4>
                </div>
                <div class="card-body">
                    <form asp-action="EditOffice" method="post">
                        <input asp-for="Id" type="hidden" />
                        <input asp-for="CompanyId" type="hidden" />
                        <input asp-for="CreatedAt" type="hidden" />
                        
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="Name" class="form-label">اسم المكتب *</label>
                                <input asp-for="Name" class="form-control" />
                                <span asp-validation-for="Name" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="Phone" class="form-label">رقم الهاتف *</label>
                                <input asp-for="Phone" class="form-control" />
                                <span asp-validation-for="Phone" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Address" class="form-label">العنوان *</label>
                            <textarea asp-for="Address" class="form-control" rows="2"></textarea>
                            <span asp-validation-for="Address" class="text-danger"></span>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="City" class="form-label">المدينة</label>
                                <input asp-for="City" class="form-control" />
                                <span asp-validation-for="City" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="Region" class="form-label">المنطقة</label>
                                <input asp-for="Region" class="form-control" />
                                <span asp-validation-for="Region" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Description" class="form-label">وصف المكتب</label>
                            <textarea asp-for="Description" class="form-control" rows="3"></textarea>
                            <span asp-validation-for="Description" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input asp-for="IsActive" class="form-check-input" />
                                <label asp-for="IsActive" class="form-check-label">
                                    المكتب نشط
                                </label>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="@Url.Action("Offices")" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> العودة
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ التغييرات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
