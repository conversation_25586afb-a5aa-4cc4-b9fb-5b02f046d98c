/* Arabic RTL Support */
html {
  font-size: 14px;
  direction: rtl;
}

@media (min-width: 768px) {
  html {
    font-size: 16px;
  }
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  margin-bottom: 60px;
  background-color: #f8f9fa;
}

/* Custom Colors */
:root {
  --primary-color: #0d6efd;
  --secondary-color: #6c757d;
  --success-color: #198754;
  --info-color: #0dcaf0;
  --warning-color: #ffc107;
  --danger-color: #dc3545;
  --light-color: #f8f9fa;
  --dark-color: #212529;
}

/* Focus States */
.btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
  box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem #258cfb;
}

/* Layout */
html {
  position: relative;
  min-height: 100%;
}

/* Cards */
.card {
  border: none;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  transition: all 0.3s ease;
}

.card:hover {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.card-header {
  background-color: var(--light-color);
  border-bottom: 1px solid rgba(0, 0, 0, 0.125);
  font-weight: 600;
}

/* Buttons */
.btn {
  border-radius: 0.375rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn:hover {
  transform: translateY(-1px);
}

.btn-lg {
  padding: 0.75rem 1.5rem;
  font-size: 1.1rem;
}

/* Navigation */
.navbar-brand {
  font-weight: 700;
  font-size: 1.5rem;
}

.navbar-nav .nav-link {
  font-weight: 500;
  transition: color 0.2s ease;
}

/* Tables */
.table {
  border-radius: 0.5rem;
  overflow: hidden;
}

.table thead th {
  border-bottom: 2px solid var(--primary-color);
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.875rem;
  letter-spacing: 0.5px;
}

/* Badges */
.badge {
  font-weight: 500;
  padding: 0.375rem 0.75rem;
}

/* Alerts */
.alert {
  border: none;
  border-radius: 0.5rem;
  font-weight: 500;
}

.alert-dismissible .btn-close {
  padding: 0.75rem 1rem;
}

/* Forms */
.form-control, .form-select {
  border-radius: 0.375rem;
  border: 1px solid #ced4da;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus, .form-select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-label {
  font-weight: 600;
  color: var(--dark-color);
  margin-bottom: 0.5rem;
}

/* Custom Utilities */
.border-left-primary {
  border-left: 4px solid var(--primary-color) !important;
}

.border-left-success {
  border-left: 4px solid var(--success-color) !important;
}

.border-left-info {
  border-left: 4px solid var(--info-color) !important;
}

.border-left-warning {
  border-left: 4px solid var(--warning-color) !important;
}

.border-left-danger {
  border-left: 4px solid var(--danger-color) !important;
}

/* Statistics Cards */
.stats-card {
  background: linear-gradient(135deg, var(--primary-color), #0056b3);
  color: white;
  border-radius: 1rem;
  padding: 1.5rem;
  text-align: center;
  transition: transform 0.3s ease;
}

.stats-card:hover {
  transform: scale(1.05);
}

.stats-card h3 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.stats-card p {
  font-size: 1.1rem;
  opacity: 0.9;
  margin-bottom: 0;
}

/* Footer */
.footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 60px;
  line-height: 60px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container-fluid {
    padding-left: 15px;
    padding-right: 15px;
  }

  .btn-lg {
    padding: 0.5rem 1rem;
    font-size: 1rem;
  }

  .card-body {
    padding: 1rem;
  }

  .table-responsive {
    font-size: 0.875rem;
  }
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.slide-in-right {
  animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
  from { opacity: 0; transform: translateX(50px); }
  to { opacity: 1; transform: translateX(0); }
}

/* Form Placeholders for RTL */
.form-floating > .form-control-plaintext::placeholder,
.form-floating > .form-control::placeholder {
  color: var(--bs-secondary-color);
  text-align: end;
}

.form-floating > .form-control-plaintext:focus::placeholder,
.form-floating > .form-control:focus::placeholder {
  text-align: start;
}