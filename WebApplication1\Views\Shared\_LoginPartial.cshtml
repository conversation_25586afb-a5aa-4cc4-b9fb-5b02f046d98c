@using Microsoft.AspNetCore.Identity
@using WebApplication1.Models
@inject SignInManager<User> SignInManager
@inject UserManager<User> UserManager

<ul class="navbar-nav">
@if (SignInManager.IsSignedIn(User))
{
    var user = await UserManager.GetUserAsync(User);
    <li class="nav-item dropdown">
        <a class="nav-link dropdown-toggle text-white" href="#" role="button" data-bs-toggle="dropdown">
            <i class="fas fa-user"></i> @user?.FullName
        </a>
        <ul class="dropdown-menu">
            @if (User.IsInRole("SystemAdmin"))
            {
                <li><a class="dropdown-item" asp-controller="Admin" asp-action="Index">
                    <i class="fas fa-tachometer-alt"></i> لوحة الإدارة
                </a></li>
            }
            @if (User.IsInRole("CompanyManager"))
            {
                <li><a class="dropdown-item" asp-controller="CompanyManager" asp-action="Index">
                    <i class="fas fa-building"></i> إدارة الشركة
                </a></li>
            }
            <li><hr class="dropdown-divider"></li>
            <li>
                <form class="form-inline" asp-area="Identity" asp-page="/Account/Logout" asp-route-returnUrl="@Url.Action("Index", "Home", new { area = "" })">
                    <button type="submit" class="dropdown-item">
                        <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                    </button>
                </form>
            </li>
        </ul>
    </li>
}
else
{
    <li class="nav-item">
        <a class="nav-link text-white" asp-area="Identity" asp-page="/Account/Login">
            <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
        </a>
    </li>
}
</ul>
