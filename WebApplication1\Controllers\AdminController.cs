using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using WebApplication1.Attributes;
using WebApplication1.Data;
using WebApplication1.Models;
using WebApplication1.Services;
using WebApplication1.ViewModels;

namespace WebApplication1.Controllers
{
    [Authorize(Roles = "SystemAdmin")]
    public class AdminController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly UserManager<User> _userManager;
        private readonly IPermissionService _permissionService;

        public AdminController(ApplicationDbContext context, UserManager<User> userManager, IPermissionService permissionService)
        {
            _context = context;
            _userManager = userManager;
            _permissionService = permissionService;
        }

        public async Task<IActionResult> Index()
        {
            var stats = new AdminDashboardViewModel
            {
                TotalCompanies = await _context.InsuranceCompanies.CountAsync(),
                TotalOffices = await _context.Offices.CountAsync(),
                TotalOffers = await _context.InsuranceOffers.CountAsync(),
                TotalManagers = await _userManager.GetUsersInRoleAsync("CompanyManager")
            };

            return View(stats);
        }

        // Companies Management
        [RequirePermission(Permissions.MANAGE_COMPANIES)]
        public async Task<IActionResult> Companies()
        {
            var companies = await _context.InsuranceCompanies
                .Include(c => c.Managers)
                .Include(c => c.Offices)
                .ToListAsync();
            return View(companies);
        }

        [RequirePermission(Permissions.MANAGE_COMPANIES)]
        public IActionResult CreateCompany()
        {
            return View();
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateCompany(InsuranceCompany company)
        {
            if (ModelState.IsValid)
            {
                _context.InsuranceCompanies.Add(company);
                await _context.SaveChangesAsync();
                TempData["Success"] = "تم إنشاء الشركة بنجاح";
                return RedirectToAction(nameof(Companies));
            }
            return View(company);
        }

        public async Task<IActionResult> EditCompany(int id)
        {
            var company = await _context.InsuranceCompanies.FindAsync(id);
            if (company == null)
            {
                return NotFound();
            }
            return View(company);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> EditCompany(int id, InsuranceCompany company)
        {
            if (id != company.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    _context.Update(company);
                    await _context.SaveChangesAsync();
                    TempData["Success"] = "تم تحديث الشركة بنجاح";
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!CompanyExists(company.Id))
                    {
                        return NotFound();
                    }
                    throw;
                }
                return RedirectToAction(nameof(Companies));
            }
            return View(company);
        }

        [HttpPost]
        public async Task<IActionResult> DeleteCompany(int id)
        {
            var company = await _context.InsuranceCompanies.FindAsync(id);
            if (company != null)
            {
                _context.InsuranceCompanies.Remove(company);
                await _context.SaveChangesAsync();
                TempData["Success"] = "تم حذف الشركة بنجاح";
            }
            return RedirectToAction(nameof(Companies));
        }

        // Company Managers Management
        public async Task<IActionResult> CreateManager(int companyId)
        {
            var company = await _context.InsuranceCompanies.FindAsync(companyId);
            if (company == null)
            {
                return NotFound();
            }

            ViewBag.Company = company;
            return View();
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateManager(CreateManagerViewModel model)
        {
            if (ModelState.IsValid)
            {
                var user = new User
                {
                    UserName = model.Email,
                    Email = model.Email,
                    FullName = model.FullName,
                    Role = UserRole.CompanyManager,
                    CompanyId = model.CompanyId,
                    EmailConfirmed = true
                };

                var result = await _userManager.CreateAsync(user, model.Password);
                if (result.Succeeded)
                {
                    await _userManager.AddToRoleAsync(user, "CompanyManager");

                    // Grant default company manager permissions
                    var currentUser = await _userManager.GetUserAsync(User);
                    await _permissionService.GrantPermissionAsync(user.Id, Permissions.MANAGE_OFFICES, currentUser?.FullName ?? "System");
                    await _permissionService.GrantPermissionAsync(user.Id, Permissions.MANAGE_OFFERS, currentUser?.FullName ?? "System");
                    await _permissionService.GrantPermissionAsync(user.Id, Permissions.VIEW_COMPANY_REPORTS, currentUser?.FullName ?? "System");

                    TempData["Success"] = "تم إنشاء حساب مدير الشركة بنجاح مع الصلاحيات الافتراضية";
                    return RedirectToAction(nameof(Companies));
                }

                foreach (var error in result.Errors)
                {
                    ModelState.AddModelError(string.Empty, error.Description);
                }
            }

            var company = await _context.InsuranceCompanies.FindAsync(model.CompanyId);
            ViewBag.Company = company;
            return View(model);
        }

        private bool CompanyExists(int id)
        {
            return _context.InsuranceCompanies.Any(e => e.Id == id);
        }
    }
}
