using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using WebApplication1.Models;

namespace WebApplication1.Data
{
    public class ApplicationDbContext : IdentityDbContext<User>
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
            : base(options)
        {
        }

        public DbSet<InsuranceCompany> InsuranceCompanies { get; set; }
        public DbSet<Office> Offices { get; set; }
        public DbSet<InsuranceOffer> InsuranceOffers { get; set; }
        public DbSet<Permission> Permissions { get; set; }
        public DbSet<UserPermission> UserPermissions { get; set; }

        protected override void OnModelCreating(ModelBuilder builder)
        {
            base.OnModelCreating(builder);

            // Configure relationships
            builder.Entity<User>()
                .HasOne(u => u.Company)
                .WithMany(c => c.Managers)
                .HasForeignKey(u => u.CompanyId)
                .OnDelete(DeleteBehavior.SetNull);

            builder.Entity<Office>()
                .HasOne(o => o.Company)
                .WithMany(c => c.Offices)
                .HasForeignKey(o => o.CompanyId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.Entity<InsuranceOffer>()
                .HasOne(io => io.Office)
                .WithMany(o => o.Offers)
                .HasForeignKey(io => io.OfficeId)
                .OnDelete(DeleteBehavior.Cascade);

            // Configure decimal precision
            builder.Entity<InsuranceOffer>()
                .Property(io => io.MinPrice)
                .HasPrecision(18, 2);

            builder.Entity<InsuranceOffer>()
                .Property(io => io.MaxPrice)
                .HasPrecision(18, 2);

            // Configure indexes for better performance
            builder.Entity<InsuranceCompany>()
                .HasIndex(c => c.Name);

            builder.Entity<Office>()
                .HasIndex(o => o.Name);

            builder.Entity<Office>()
                .HasIndex(o => o.City);

            builder.Entity<InsuranceOffer>()
                .HasIndex(io => io.Title);

            // Configure Permission relationships
            builder.Entity<UserPermission>()
                .HasOne(up => up.User)
                .WithMany(u => u.UserPermissions)
                .HasForeignKey(up => up.UserId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.Entity<UserPermission>()
                .HasOne(up => up.Permission)
                .WithMany(p => p.UserPermissions)
                .HasForeignKey(up => up.PermissionId)
                .OnDelete(DeleteBehavior.Cascade);

            // Configure unique constraint for user-permission combination
            builder.Entity<UserPermission>()
                .HasIndex(up => new { up.UserId, up.PermissionId })
                .IsUnique();

            // Configure indexes for better performance
            builder.Entity<Permission>()
                .HasIndex(p => p.Name)
                .IsUnique();

            builder.Entity<Permission>()
                .HasIndex(p => p.Category);
        }
    }
}
