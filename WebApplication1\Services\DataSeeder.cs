using Microsoft.AspNetCore.Identity;
using WebApplication1.Data;
using WebApplication1.Models;

namespace WebApplication1.Services
{
    public class DataSeeder
    {
        private readonly ApplicationDbContext _context;
        private readonly UserManager<User> _userManager;
        private readonly RoleManager<IdentityRole> _roleManager;
        private readonly IPermissionService _permissionService;

        public DataSeeder(ApplicationDbContext context, UserManager<User> userManager, RoleManager<IdentityRole> roleManager, IPermissionService permissionService)
        {
            _context = context;
            _userManager = userManager;
            _roleManager = roleManager;
            _permissionService = permissionService;
        }

        public async Task SeedAsync()
        {
            // Create roles
            await CreateRolesAsync();

            // Seed permissions
            await _permissionService.SeedPermissionsAsync();

            // Create admin user
            await CreateAdminUserAsync();

            // Create sample data
            await CreateSampleDataAsync();
        }

        private async Task CreateRolesAsync()
        {
            string[] roles = { "SystemAdmin", "CompanyManager" };

            foreach (var role in roles)
            {
                if (!await _roleManager.RoleExistsAsync(role))
                {
                    await _roleManager.CreateAsync(new IdentityRole(role));
                }
            }
        }

        private async Task CreateAdminUserAsync()
        {
            var adminEmail = "<EMAIL>";
            var adminUser = await _userManager.FindByEmailAsync(adminEmail);

            if (adminUser == null)
            {
                adminUser = new User
                {
                    UserName = adminEmail,
                    Email = adminEmail,
                    FullName = "مدير النظام",
                    Role = UserRole.SystemAdmin,
                    EmailConfirmed = true
                };

                var result = await _userManager.CreateAsync(adminUser, "Admin123!");
                if (result.Succeeded)
                {
                    await _userManager.AddToRoleAsync(adminUser, "SystemAdmin");

                    // Grant all system admin permissions
                    await _permissionService.GrantPermissionAsync(adminUser.Id, Permissions.MANAGE_COMPANIES, "System");
                    await _permissionService.GrantPermissionAsync(adminUser.Id, Permissions.CREATE_COMPANY_MANAGERS, "System");
                    await _permissionService.GrantPermissionAsync(adminUser.Id, Permissions.VIEW_SYSTEM_REPORTS, "System");
                    await _permissionService.GrantPermissionAsync(adminUser.Id, Permissions.MANAGE_SYSTEM_SETTINGS, "System");
                    await _permissionService.GrantPermissionAsync(adminUser.Id, Permissions.DELETE_COMPANIES, "System");
                }
            }
        }

        private async Task CreateSampleDataAsync()
        {
            if (!_context.InsuranceCompanies.Any())
            {
                var companies = new List<InsuranceCompany>
                {
                    new InsuranceCompany
                    {
                        Name = "شركة التأمين الأولى",
                        Description = "شركة رائدة في مجال تأمين السيارات",
                        Phone = "0112345678",
                        Email = "<EMAIL>",
                        Address = "الرياض، المملكة العربية السعودية",
                        Website = "www.company1.com"
                    },
                    new InsuranceCompany
                    {
                        Name = "شركة الحماية للتأمين",
                        Description = "خدمات تأمين شاملة وموثوقة",
                        Phone = "0112345679",
                        Email = "<EMAIL>",
                        Address = "جدة، المملكة العربية السعودية",
                        Website = "www.protection.com"
                    }
                };

                _context.InsuranceCompanies.AddRange(companies);
                await _context.SaveChangesAsync();

                // Add sample offices
                var company1 = companies[0];
                var offices = new List<Office>
                {
                    new Office
                    {
                        Name = "مكتب الرياض الرئيسي",
                        Address = "شارع الملك فهد، الرياض",
                        Phone = "0112345680",
                        City = "الرياض",
                        Region = "الرياض",
                        Description = "المكتب الرئيسي في الرياض",
                        CompanyId = company1.Id
                    },
                    new Office
                    {
                        Name = "مكتب الرياض الفرعي",
                        Address = "حي العليا، الرياض",
                        Phone = "0112345681",
                        City = "الرياض",
                        Region = "الرياض",
                        Description = "مكتب فرعي في حي العليا",
                        CompanyId = company1.Id
                    }
                };

                _context.Offices.AddRange(offices);
                await _context.SaveChangesAsync();

                // Add sample offers
                var office1 = offices[0];
                var offers = new List<InsuranceOffer>
                {
                    new InsuranceOffer
                    {
                        Title = "تأمين شامل للسيارات",
                        Description = "تغطية شاملة ضد جميع المخاطر",
                        Coverage = "شامل",
                        MinPrice = 1200,
                        MaxPrice = 3000,
                        Terms = "يشمل التأمين ضد الحوادث والسرقة والحريق",
                        OfficeId = office1.Id
                    },
                    new InsuranceOffer
                    {
                        Title = "تأمين ضد الغير",
                        Description = "تأمين إجباري ضد الغير",
                        Coverage = "ضد الغير",
                        MinPrice = 400,
                        MaxPrice = 800,
                        Terms = "تأمين إجباري حسب نظام المرور السعودي",
                        OfficeId = office1.Id
                    }
                };

                _context.InsuranceOffers.AddRange(offers);
                await _context.SaveChangesAsync();
            }
        }
    }
}
