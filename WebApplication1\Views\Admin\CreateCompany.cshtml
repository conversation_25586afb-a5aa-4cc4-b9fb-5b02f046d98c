@model WebApplication1.Models.InsuranceCompany
@{
    ViewData["Title"] = "إضافة شركة جديدة";
}

<div class="container">
    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-plus"></i> إضافة شركة تأمين جديدة
                    </h4>
                </div>
                <div class="card-body">
                    <form asp-action="CreateCompany" method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="Name" class="form-label">اسم الشركة *</label>
                                <input asp-for="Name" class="form-control" />
                                <span asp-validation-for="Name" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="Phone" class="form-label">رقم الهاتف</label>
                                <input asp-for="Phone" class="form-control" />
                                <span asp-validation-for="Phone" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="Email" class="form-label">البريد الإلكتروني</label>
                                <input asp-for="Email" type="email" class="form-control" />
                                <span asp-validation-for="Email" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="Website" class="form-label">الموقع الإلكتروني</label>
                                <input asp-for="Website" class="form-control" placeholder="https://example.com" />
                                <span asp-validation-for="Website" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Address" class="form-label">العنوان</label>
                            <textarea asp-for="Address" class="form-control" rows="2"></textarea>
                            <span asp-validation-for="Address" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Description" class="form-label">وصف الشركة</label>
                            <textarea asp-for="Description" class="form-control" rows="3"></textarea>
                            <span asp-validation-for="Description" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input asp-for="IsActive" class="form-check-input" checked />
                                <label asp-for="IsActive" class="form-check-label">
                                    الشركة نشطة
                                </label>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="@Url.Action("Companies")" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> العودة
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save"></i> حفظ الشركة
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
