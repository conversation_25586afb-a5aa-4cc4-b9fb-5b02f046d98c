using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using WebApplication1.Attributes;
using WebApplication1.Data;
using WebApplication1.Models;
using WebApplication1.Services;
using WebApplication1.ViewModels;

namespace WebApplication1.Controllers
{
    [Authorize]
    public class PermissionController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly UserManager<User> _userManager;
        private readonly IPermissionService _permissionService;

        public PermissionController(ApplicationDbContext context, UserManager<User> userManager, IPermissionService permissionService)
        {
            _context = context;
            _userManager = userManager;
            _permissionService = permissionService;
        }

        [RequirePermission(Permissions.MANAGE_SYSTEM_SETTINGS)]
        public async Task<IActionResult> Index()
        {
            var permissions = await _permissionService.GetAllPermissionsAsync();
            return View(permissions);
        }

        [RequirePermission(Permissions.CREATE_COMPANY_MANAGERS)]
        public async Task<IActionResult> ManageUserPermissions(string userId)
        {
            var user = await _userManager.FindByIdAsync(userId);
            if (user == null)
            {
                return NotFound();
            }

            var userPermissions = await _permissionService.GetUserPermissionsAsync(userId);
            var allPermissions = await _permissionService.GetAllPermissionsAsync();

            var viewModel = new UserPermissionsViewModel
            {
                User = user,
                UserPermissions = userPermissions,
                AllPermissions = allPermissions
            };

            return View(viewModel);
        }

        [HttpPost]
        [RequirePermission(Permissions.CREATE_COMPANY_MANAGERS)]
        public async Task<IActionResult> GrantPermission(string userId, string permissionName)
        {
            var currentUser = await _userManager.GetUserAsync(User);
            var result = await _permissionService.GrantPermissionAsync(userId, permissionName, currentUser?.FullName ?? "System");

            if (result)
            {
                TempData["Success"] = "تم منح الصلاحية بنجاح";
            }
            else
            {
                TempData["Error"] = "فشل في منح الصلاحية";
            }

            return RedirectToAction(nameof(ManageUserPermissions), new { userId });
        }

        [HttpPost]
        [RequirePermission(Permissions.CREATE_COMPANY_MANAGERS)]
        public async Task<IActionResult> RevokePermission(string userId, string permissionName)
        {
            var result = await _permissionService.RevokePermissionAsync(userId, permissionName);

            if (result)
            {
                TempData["Success"] = "تم إلغاء الصلاحية بنجاح";
            }
            else
            {
                TempData["Error"] = "فشل في إلغاء الصلاحية";
            }

            return RedirectToAction(nameof(ManageUserPermissions), new { userId });
        }

        // API endpoint to check if current user has permission
        [HttpGet]
        public async Task<IActionResult> CheckPermission(string permissionName)
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return Json(new { hasPermission = false });
            }

            var hasPermission = await _permissionService.HasPermissionAsync(user.Id, permissionName);
            return Json(new { hasPermission });
        }

        // Get permissions by category for AJAX calls
        [HttpGet]
        public async Task<IActionResult> GetPermissionsByCategory(string category)
        {
            var permissions = await _permissionService.GetPermissionsByCategoryAsync(category);
            return Json(permissions.Select(p => new { p.Id, p.Name, p.Description }));
        }
    }
}
