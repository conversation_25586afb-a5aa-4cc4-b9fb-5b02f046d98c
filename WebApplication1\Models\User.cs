using Microsoft.AspNetCore.Identity;
using System.ComponentModel.DataAnnotations;

namespace WebApplication1.Models
{
    public class User : IdentityUser
    {
        [Required]
        [StringLength(100)]
        public string FullName { get; set; } = string.Empty;

        [Required]
        public UserRole Role { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation property for company manager
        public int? CompanyId { get; set; }
        public virtual InsuranceCompany? Company { get; set; }

        // Navigation property for permissions
        public virtual ICollection<UserPermission> UserPermissions { get; set; } = new List<UserPermission>();
    }

    public enum UserRole
    {
        SystemAdmin = 1,
        CompanyManager = 2
    }
}
