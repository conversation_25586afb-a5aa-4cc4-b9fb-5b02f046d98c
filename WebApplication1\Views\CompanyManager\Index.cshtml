@model WebApplication1.ViewModels.CompanyDashboardViewModel
@{
    ViewData["Title"] = "لوحة إدارة الشركة";
}

<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="text-primary">
                <i class="fas fa-building"></i> لوحة إدارة شركة @Model.Company.Name
            </h2>
            <p class="text-muted">مرحباً بك في لوحة التحكم الخاصة بشركتك</p>
        </div>
    </div>

    <!-- Company Info Card -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-primary">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle"></i> معلومات الشركة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>الاسم:</strong> @Model.Company.Name</p>
                            @if (!string.IsNullOrEmpty(Model.Company.Description))
                            {
                                <p><strong>الوصف:</strong> @Model.Company.Description</p>
                            }
                        </div>
                        <div class="col-md-6">
                            @if (!string.IsNullOrEmpty(Model.Company.Phone))
                            {
                                <p><strong>الهاتف:</strong> @Model.Company.Phone</p>
                            }
                            @if (!string.IsNullOrEmpty(Model.Company.Email))
                            {
                                <p><strong>البريد الإلكتروني:</strong> @Model.Company.Email</p>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">@Model.TotalOffices</h4>
                            <p class="mb-0">إجمالي المكاتب</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-map-marker-alt fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">@Model.ActiveOffices</h4>
                            <p class="mb-0">المكاتب النشطة</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">@Model.TotalOffers</h4>
                            <p class="mb-0">إجمالي العروض</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-tags fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">@Model.ActiveOffers</h4>
                            <p class="mb-0">العروض النشطة</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-star fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt"></i> الإجراءات السريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="@Url.Action("Offices")" class="btn btn-primary btn-lg w-100">
                                <i class="fas fa-building"></i><br>
                                إدارة المكاتب
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="@Url.Action("CreateOffice")" class="btn btn-success btn-lg w-100">
                                <i class="fas fa-plus"></i><br>
                                إضافة مكتب جديد
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="@Url.Action("Offers")" class="btn btn-info btn-lg w-100">
                                <i class="fas fa-tags"></i><br>
                                إدارة العروض
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="@Url.Action("CreateOffer")" class="btn btn-warning btn-lg w-100">
                                <i class="fas fa-plus-circle"></i><br>
                                إضافة عرض جديد
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Offices -->
    @if (Model.Company.Offices.Any())
    {
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-map-marker-alt"></i> المكاتب الحديثة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            @foreach (var office in Model.Company.Offices.OrderByDescending(o => o.CreatedAt).Take(3))
                            {
                                <div class="col-md-4 mb-3">
                                    <div class="card border-left-primary">
                                        <div class="card-body">
                                            <h6 class="text-primary">@office.Name</h6>
                                            <p class="small text-muted mb-1">@office.Address</p>
                                            <p class="small mb-2">
                                                <i class="fas fa-phone"></i> @office.Phone
                                            </p>
                                            <span class="badge @(office.IsActive ? "bg-success" : "bg-secondary")">
                                                @(office.IsActive ? "نشط" : "غير نشط")
                                            </span>
                                            <span class="badge bg-info">
                                                @office.Offers.Count(o => o.IsActive) عرض
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
</div>
