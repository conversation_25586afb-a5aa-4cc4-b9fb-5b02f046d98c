/* Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-bddmc6yalx] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-bddmc6yalx] {
  color: #0077cc;
}

.btn-primary[b-bddmc6yalx] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-bddmc6yalx], .nav-pills .show > .nav-link[b-bddmc6yalx] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-bddmc6yalx] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-bddmc6yalx] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-bddmc6yalx] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-bddmc6yalx] {
  font-size: 1rem;
  line-height: inherit;
}

.footer[b-bddmc6yalx] {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}
