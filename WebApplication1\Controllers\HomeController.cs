using System.Diagnostics;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using WebApplication1.Data;
using WebApplication1.Models;

namespace WebApplication1.Controllers;

public class HomeController : Controller
{
    private readonly ILogger<HomeController> _logger;
    private readonly ApplicationDbContext _context;

    public HomeController(ILogger<HomeController> logger, ApplicationDbContext context)
    {
        _logger = logger;
        _context = context;
    }

    public async Task<IActionResult> Index(string searchTerm = "")
    {
        var companies = _context.InsuranceCompanies
            .Include(c => c.Offices)
            .ThenInclude(o => o.Offers)
            .Where(c => c.IsActive);

        if (!string.IsNullOrEmpty(searchTerm))
        {
            companies = companies.Where(c => c.Name.Contains(searchTerm) ||
                                           c.Description.Contains(searchTerm));
        }

        ViewBag.SearchTerm = searchTerm;
        return View(await companies.ToListAsync());
    }

    public IActionResult Privacy()
    {
        return View();
    }

    [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
    public IActionResult Error()
    {
        return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
    }
}
