@model IEnumerable<WebApplication1.Models.Office>
@{
    ViewData["Title"] = "إدارة المكاتب";
}

<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-8">
            <h2 class="text-primary">
                <i class="fas fa-building"></i> إدارة المكاتب
            </h2>
        </div>
        <div class="col-md-4 text-end">
            <a href="@Url.Action("CreateOffice")" class="btn btn-success">
                <i class="fas fa-plus"></i> إضافة مكتب جديد
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    @if (Model.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>اسم المكتب</th>
                                        <th>العنوان</th>
                                        <th>المدينة</th>
                                        <th>الهاتف</th>
                                        <th>عدد العروض</th>
                                        <th>الحالة</th>
                                        <th>تاريخ الإنشاء</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var office in Model)
                                    {
                                        <tr>
                                            <td>
                                                <strong>@office.Name</strong>
                                                @if (!string.IsNullOrEmpty(office.Description))
                                                {
                                                    <br><small class="text-muted">@office.Description</small>
                                                }
                                            </td>
                                            <td>@office.Address</td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(office.City))
                                                {
                                                    <span class="badge bg-info">@office.City</span>
                                                }
                                                @if (!string.IsNullOrEmpty(office.Region))
                                                {
                                                    <br><small class="text-muted">@office.Region</small>
                                                }
                                            </td>
                                            <td>
                                                <a href="tel:@office.Phone" class="text-decoration-none">
                                                    @office.Phone
                                                </a>
                                            </td>
                                            <td>
                                                <span class="badge bg-primary">
                                                    @office.Offers.Count(o => o.IsActive)
                                                </span>
                                                @if (office.Offers.Any(o => !o.IsActive))
                                                {
                                                    <span class="badge bg-secondary">
                                                        @office.Offers.Count(o => !o.IsActive) غير نشط
                                                    </span>
                                                }
                                            </td>
                                            <td>
                                                @if (office.IsActive)
                                                {
                                                    <span class="badge bg-success">نشط</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-danger">غير نشط</span>
                                                }
                                            </td>
                                            <td>
                                                <small>@office.CreatedAt.ToString("dd/MM/yyyy")</small>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="@Url.Action("EditOffice", new { id = office.Id })" 
                                                       class="btn btn-sm btn-outline-primary" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a href="@Url.Action("Offers", new { officeId = office.Id })" 
                                                       class="btn btn-sm btn-outline-info" title="عرض العروض">
                                                        <i class="fas fa-tags"></i>
                                                    </a>
                                                    <form method="post" asp-action="DeleteOffice" asp-route-id="@office.Id" 
                                                          style="display: inline;" 
                                                          onsubmit="return confirm('هل أنت متأكد من حذف هذا المكتب؟ سيتم حذف جميع العروض المرتبطة به.')">
                                                        <button type="submit" class="btn btn-sm btn-outline-danger" title="حذف">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-building fa-3x text-muted mb-3"></i>
                            <h4 class="text-muted">لا توجد مكاتب مسجلة</h4>
                            <p class="text-muted">ابدأ بإضافة مكتب جديد لشركتك</p>
                            <a href="@Url.Action("CreateOffice")" class="btn btn-primary">
                                <i class="fas fa-plus"></i> إضافة مكتب جديد
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>
