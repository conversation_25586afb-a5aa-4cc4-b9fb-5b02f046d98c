@model WebApplication1.ViewModels.UserPermissionsViewModel
@{
    ViewData["Title"] = "إدارة صلاحيات المستخدم";
}

<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="text-primary">
                <i class="fas fa-user-shield"></i> إدارة صلاحيات المستخدم
            </h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="@Url.Action("Index", "Admin")">لوحة الإدارة</a></li>
                    <li class="breadcrumb-item"><a href="@Url.Action("Companies", "Admin")">الشركات</a></li>
                    <li class="breadcrumb-item active">إدارة الصلاحيات</li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- User Info Card -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-primary">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-user"></i> معلومات المستخدم
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>الاسم:</strong> @Model.User.FullName</p>
                            <p><strong>البريد الإلكتروني:</strong> @Model.User.Email</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>الدور:</strong> 
                                <span class="badge bg-info">
                                    @(Model.User.Role == UserRole.SystemAdmin ? "مدير النظام" : "مدير الشركة")
                                </span>
                            </p>
                            <p><strong>تاريخ الإنشاء:</strong> @Model.User.CreatedAt.ToString("dd/MM/yyyy")</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Current Permissions -->
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-check-circle"></i> الصلاحيات الحالية (@Model.UserPermissions.Count)
                    </h5>
                </div>
                <div class="card-body">
                    @if (Model.UserPermissions.Any())
                    {
                        @foreach (var category in Model.UserPermissions.GroupBy(p => p.Category))
                        {
                            <div class="mb-3">
                                <h6 class="text-primary border-bottom pb-2">
                                    @(category.Key == "System" ? "صلاحيات النظام" : 
                                      category.Key == "Company" ? "صلاحيات الشركة" : "صلاحيات المكتب")
                                </h6>
                                @foreach (var permission in category)
                                {
                                    <div class="d-flex justify-content-between align-items-center mb-2 p-2 bg-light rounded">
                                        <div>
                                            <strong>@permission.Description</strong>
                                            <br><small class="text-muted">@permission.Name</small>
                                        </div>
                                        <form method="post" asp-action="RevokePermission" style="display: inline;">
                                            <input type="hidden" name="userId" value="@Model.User.Id" />
                                            <input type="hidden" name="permissionName" value="@permission.Name" />
                                            <button type="submit" class="btn btn-sm btn-outline-danger" 
                                                    onclick="return confirm('هل أنت متأكد من إلغاء هذه الصلاحية؟')">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </form>
                                    </div>
                                }
                            </div>
                        }
                    }
                    else
                    {
                        <div class="text-center py-4">
                            <i class="fas fa-exclamation-triangle fa-2x text-warning mb-3"></i>
                            <p class="text-muted">لا توجد صلاحيات مخصصة لهذا المستخدم</p>
                        </div>
                    }
                </div>
            </div>
        </div>

        <!-- Available Permissions -->
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-plus-circle"></i> الصلاحيات المتاحة
                    </h5>
                </div>
                <div class="card-body">
                    @{
                        var availablePermissions = Model.GetAvailablePermissions();
                    }
                    @if (availablePermissions.Any())
                    {
                        @foreach (var category in availablePermissions.GroupBy(p => p.Category))
                        {
                            <div class="mb-3">
                                <h6 class="text-primary border-bottom pb-2">
                                    @(category.Key == "System" ? "صلاحيات النظام" : 
                                      category.Key == "Company" ? "صلاحيات الشركة" : "صلاحيات المكتب")
                                </h6>
                                @foreach (var permission in category)
                                {
                                    <div class="d-flex justify-content-between align-items-center mb-2 p-2 border rounded">
                                        <div>
                                            <strong>@permission.Description</strong>
                                            <br><small class="text-muted">@permission.Name</small>
                                        </div>
                                        <form method="post" asp-action="GrantPermission" style="display: inline;">
                                            <input type="hidden" name="userId" value="@Model.User.Id" />
                                            <input type="hidden" name="permissionName" value="@permission.Name" />
                                            <button type="submit" class="btn btn-sm btn-outline-success">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </form>
                                    </div>
                                }
                            </div>
                        }
                    }
                    else
                    {
                        <div class="text-center py-4">
                            <i class="fas fa-check-circle fa-2x text-success mb-3"></i>
                            <p class="text-muted">تم منح جميع الصلاحيات المتاحة لهذا المستخدم</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt"></i> إجراءات سريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="btn-group" role="group">
                        @if (Model.User.Role == UserRole.SystemAdmin)
                        {
                            <button type="button" class="btn btn-outline-primary" onclick="grantAllSystemPermissions()">
                                <i class="fas fa-crown"></i> منح جميع صلاحيات النظام
                            </button>
                        }
                        @if (Model.User.Role == UserRole.CompanyManager)
                        {
                            <button type="button" class="btn btn-outline-success" onclick="grantAllCompanyPermissions()">
                                <i class="fas fa-building"></i> منح جميع صلاحيات الشركة
                            </button>
                        }
                        <button type="button" class="btn btn-outline-danger" onclick="revokeAllPermissions()">
                            <i class="fas fa-ban"></i> إلغاء جميع الصلاحيات
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function grantAllSystemPermissions() {
            if (confirm('هل أنت متأكد من منح جميع صلاحيات النظام؟')) {
                // Implementation for granting all system permissions
                alert('سيتم تنفيذ هذه الميزة قريباً');
            }
        }

        function grantAllCompanyPermissions() {
            if (confirm('هل أنت متأكد من منح جميع صلاحيات الشركة؟')) {
                // Implementation for granting all company permissions
                alert('سيتم تنفيذ هذه الميزة قريباً');
            }
        }

        function revokeAllPermissions() {
            if (confirm('هل أنت متأكد من إلغاء جميع الصلاحيات؟ هذا الإجراء لا يمكن التراجع عنه!')) {
                // Implementation for revoking all permissions
                alert('سيتم تنفيذ هذه الميزة قريباً');
            }
        }
    </script>
}
