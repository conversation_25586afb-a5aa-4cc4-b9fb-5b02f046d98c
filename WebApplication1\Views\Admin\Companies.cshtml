@model IEnumerable<WebApplication1.Models.InsuranceCompany>
@{
    ViewData["Title"] = "إدارة الشركات";
}

<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-8">
            <h2 class="text-primary">
                <i class="fas fa-building"></i> إدارة شركات التأمين
            </h2>
        </div>
        <div class="col-md-4 text-end">
            <a href="@Url.Action("CreateCompany")" class="btn btn-success">
                <i class="fas fa-plus"></i> إضافة شركة جديدة
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    @if (Model.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>اسم الشركة</th>
                                        <th>الوصف</th>
                                        <th>الهاتف</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>عدد المكاتب</th>
                                        <th>عدد المديرين</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var company in Model)
                                    {
                                        <tr>
                                            <td>
                                                <strong>@company.Name</strong>
                                                @if (!string.IsNullOrEmpty(company.Website))
                                                {
                                                    <br><small><a href="@company.Website" target="_blank">@company.Website</a></small>
                                                }
                                            </td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(company.Description))
                                                {
                                                    @(company.Description.Length > 50 ? company.Description.Substring(0, 50) + "..." : company.Description)
                                                }
                                            </td>
                                            <td>@company.Phone</td>
                                            <td>@company.Email</td>
                                            <td>
                                                <span class="badge bg-info">@company.Offices.Count</span>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary">@company.Managers.Count</span>
                                            </td>
                                            <td>
                                                @if (company.IsActive)
                                                {
                                                    <span class="badge bg-success">نشط</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-danger">غير نشط</span>
                                                }
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="@Url.Action("EditCompany", new { id = company.Id })"
                                                       class="btn btn-sm btn-outline-primary" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a href="@Url.Action("CreateManager", new { companyId = company.Id })"
                                                       class="btn btn-sm btn-outline-success" title="إضافة مدير">
                                                        <i class="fas fa-user-plus"></i>
                                                    </a>
                                                    @if (company.Managers.Any())
                                                    {
                                                        <div class="btn-group" role="group">
                                                            <button type="button" class="btn btn-sm btn-outline-info dropdown-toggle"
                                                                    data-bs-toggle="dropdown" title="إدارة الصلاحيات">
                                                                <i class="fas fa-user-shield"></i>
                                                            </button>
                                                            <ul class="dropdown-menu">
                                                                @foreach (var manager in company.Managers)
                                                                {
                                                                    <li>
                                                                        <a class="dropdown-item"
                                                                           href="@Url.Action("ManageUserPermissions", "Permission", new { userId = manager.Id })">
                                                                            <i class="fas fa-user"></i> @manager.FullName
                                                                        </a>
                                                                    </li>
                                                                }
                                                            </ul>
                                                        </div>
                                                    }
                                                    <form method="post" asp-action="DeleteCompany" asp-route-id="@company.Id"
                                                          style="display: inline;"
                                                          onsubmit="return confirm('هل أنت متأكد من حذف هذه الشركة؟')">
                                                        <button type="submit" class="btn btn-sm btn-outline-danger" title="حذف">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-building fa-3x text-muted mb-3"></i>
                            <h4 class="text-muted">لا توجد شركات مسجلة</h4>
                            <p class="text-muted">ابدأ بإضافة شركة تأمين جديدة</p>
                            <a href="@Url.Action("CreateCompany")" class="btn btn-primary">
                                <i class="fas fa-plus"></i> إضافة شركة جديدة
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>
