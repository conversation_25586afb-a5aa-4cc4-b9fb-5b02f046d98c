using System.ComponentModel.DataAnnotations;

namespace WebApplication1.Models
{
    public class Permission
    {
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [StringLength(200)]
        public string Description { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string Category { get; set; } = string.Empty;

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation properties
        public virtual ICollection<UserPermission> UserPermissions { get; set; } = new List<UserPermission>();
    }

    public class UserPermission
    {
        public int Id { get; set; }

        [Required]
        public string UserId { get; set; } = string.Empty;

        [Required]
        public int PermissionId { get; set; }

        public DateTime GrantedAt { get; set; } = DateTime.Now;

        [StringLength(100)]
        public string? GrantedBy { get; set; }

        // Navigation properties
        public virtual User User { get; set; } = null!;
        public virtual Permission Permission { get; set; } = null!;
    }

    // Static class for permission constants
    public static class Permissions
    {
        // System Admin Permissions
        public const string MANAGE_COMPANIES = "MANAGE_COMPANIES";
        public const string CREATE_COMPANY_MANAGERS = "CREATE_COMPANY_MANAGERS";
        public const string VIEW_SYSTEM_REPORTS = "VIEW_SYSTEM_REPORTS";
        public const string MANAGE_SYSTEM_SETTINGS = "MANAGE_SYSTEM_SETTINGS";
        public const string DELETE_COMPANIES = "DELETE_COMPANIES";

        // Company Manager Permissions
        public const string MANAGE_OFFICES = "MANAGE_OFFICES";
        public const string MANAGE_OFFERS = "MANAGE_OFFERS";
        public const string CREATE_OFFICE_USERS = "CREATE_OFFICE_USERS";
        public const string VIEW_COMPANY_REPORTS = "VIEW_COMPANY_REPORTS";
        public const string MANAGE_COMPANY_SETTINGS = "MANAGE_COMPANY_SETTINGS";

        // Office User Permissions (for future use)
        public const string VIEW_OFFERS = "VIEW_OFFERS";
        public const string EDIT_OFFERS = "EDIT_OFFERS";
        public const string CREATE_OFFERS = "CREATE_OFFERS";

        // Categories
        public const string CATEGORY_SYSTEM = "System";
        public const string CATEGORY_COMPANY = "Company";
        public const string CATEGORY_OFFICE = "Office";
    }
}
