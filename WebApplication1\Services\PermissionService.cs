using Microsoft.EntityFrameworkCore;
using WebApplication1.Data;
using WebApplication1.Models;

namespace WebApplication1.Services
{
    public interface IPermissionService
    {
        Task<bool> HasPermissionAsync(string userId, string permissionName);
        Task<List<Permission>> GetUserPermissionsAsync(string userId);
        Task<bool> GrantPermissionAsync(string userId, string permissionName, string grantedBy);
        Task<bool> RevokePermissionAsync(string userId, string permissionName);
        Task<List<Permission>> GetAllPermissionsAsync();
        Task<List<Permission>> GetPermissionsByCategoryAsync(string category);
        Task SeedPermissionsAsync();
    }

    public class PermissionService : IPermissionService
    {
        private readonly ApplicationDbContext _context;

        public PermissionService(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<bool> HasPermissionAsync(string userId, string permissionName)
        {
            return await _context.UserPermissions
                .Include(up => up.Permission)
                .AnyAsync(up => up.UserId == userId && 
                               up.Permission.Name == permissionName && 
                               up.Permission.IsActive);
        }

        public async Task<List<Permission>> GetUserPermissionsAsync(string userId)
        {
            return await _context.UserPermissions
                .Include(up => up.Permission)
                .Where(up => up.UserId == userId && up.Permission.IsActive)
                .Select(up => up.Permission)
                .ToListAsync();
        }

        public async Task<bool> GrantPermissionAsync(string userId, string permissionName, string grantedBy)
        {
            var permission = await _context.Permissions
                .FirstOrDefaultAsync(p => p.Name == permissionName && p.IsActive);

            if (permission == null)
                return false;

            var existingPermission = await _context.UserPermissions
                .FirstOrDefaultAsync(up => up.UserId == userId && up.PermissionId == permission.Id);

            if (existingPermission != null)
                return true; // Already has permission

            var userPermission = new UserPermission
            {
                UserId = userId,
                PermissionId = permission.Id,
                GrantedBy = grantedBy,
                GrantedAt = DateTime.Now
            };

            _context.UserPermissions.Add(userPermission);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> RevokePermissionAsync(string userId, string permissionName)
        {
            var userPermission = await _context.UserPermissions
                .Include(up => up.Permission)
                .FirstOrDefaultAsync(up => up.UserId == userId && up.Permission.Name == permissionName);

            if (userPermission == null)
                return false;

            _context.UserPermissions.Remove(userPermission);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<List<Permission>> GetAllPermissionsAsync()
        {
            return await _context.Permissions
                .Where(p => p.IsActive)
                .OrderBy(p => p.Category)
                .ThenBy(p => p.Name)
                .ToListAsync();
        }

        public async Task<List<Permission>> GetPermissionsByCategoryAsync(string category)
        {
            return await _context.Permissions
                .Where(p => p.Category == category && p.IsActive)
                .OrderBy(p => p.Name)
                .ToListAsync();
        }

        public async Task SeedPermissionsAsync()
        {
            var permissions = new List<Permission>
            {
                // System Admin Permissions
                new Permission
                {
                    Name = Permissions.MANAGE_COMPANIES,
                    Description = "إدارة شركات التأمين (إضافة، تعديل، حذف)",
                    Category = Permissions.CATEGORY_SYSTEM
                },
                new Permission
                {
                    Name = Permissions.CREATE_COMPANY_MANAGERS,
                    Description = "إنشاء حسابات مديري الشركات",
                    Category = Permissions.CATEGORY_SYSTEM
                },
                new Permission
                {
                    Name = Permissions.VIEW_SYSTEM_REPORTS,
                    Description = "عرض تقارير النظام",
                    Category = Permissions.CATEGORY_SYSTEM
                },
                new Permission
                {
                    Name = Permissions.MANAGE_SYSTEM_SETTINGS,
                    Description = "إدارة إعدادات النظام",
                    Category = Permissions.CATEGORY_SYSTEM
                },
                new Permission
                {
                    Name = Permissions.DELETE_COMPANIES,
                    Description = "حذف شركات التأمين",
                    Category = Permissions.CATEGORY_SYSTEM
                },

                // Company Manager Permissions
                new Permission
                {
                    Name = Permissions.MANAGE_OFFICES,
                    Description = "إدارة مكاتب الشركة (إضافة، تعديل، حذف)",
                    Category = Permissions.CATEGORY_COMPANY
                },
                new Permission
                {
                    Name = Permissions.MANAGE_OFFERS,
                    Description = "إدارة عروض التأمين",
                    Category = Permissions.CATEGORY_COMPANY
                },
                new Permission
                {
                    Name = Permissions.CREATE_OFFICE_USERS,
                    Description = "إنشاء حسابات موظفي المكاتب",
                    Category = Permissions.CATEGORY_COMPANY
                },
                new Permission
                {
                    Name = Permissions.VIEW_COMPANY_REPORTS,
                    Description = "عرض تقارير الشركة",
                    Category = Permissions.CATEGORY_COMPANY
                },
                new Permission
                {
                    Name = Permissions.MANAGE_COMPANY_SETTINGS,
                    Description = "إدارة إعدادات الشركة",
                    Category = Permissions.CATEGORY_COMPANY
                },

                // Office User Permissions
                new Permission
                {
                    Name = Permissions.VIEW_OFFERS,
                    Description = "عرض العروض",
                    Category = Permissions.CATEGORY_OFFICE
                },
                new Permission
                {
                    Name = Permissions.EDIT_OFFERS,
                    Description = "تعديل العروض",
                    Category = Permissions.CATEGORY_OFFICE
                },
                new Permission
                {
                    Name = Permissions.CREATE_OFFERS,
                    Description = "إنشاء عروض جديدة",
                    Category = Permissions.CATEGORY_OFFICE
                }
            };

            foreach (var permission in permissions)
            {
                var existingPermission = await _context.Permissions
                    .FirstOrDefaultAsync(p => p.Name == permission.Name);

                if (existingPermission == null)
                {
                    _context.Permissions.Add(permission);
                }
            }

            await _context.SaveChangesAsync();
        }
    }
}
