@model WebApplication1.Models.InsuranceOffer
@{
    ViewData["Title"] = "إضافة عرض جديد";
    var offices = ViewBag.Offices as List<WebApplication1.Models.Office>;
}

<div class="container">
    <div class="row">
        <div class="col-md-10 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-plus"></i> إضافة عرض تأمين جديد
                    </h4>
                </div>
                <div class="card-body">
                    <form asp-action="CreateOffer" method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
                        
                        <div class="row">
                            <div class="col-md-8 mb-3">
                                <label asp-for="Title" class="form-label">عنوان العرض *</label>
                                <input asp-for="Title" class="form-control" />
                                <span asp-validation-for="Title" class="text-danger"></span>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label asp-for="OfficeId" class="form-label">المكتب *</label>
                                <select asp-for="OfficeId" class="form-select">
                                    <option value="">اختر المكتب</option>
                                    @if (offices != null)
                                    {
                                        @foreach (var office in offices)
                                        {
                                            <option value="@office.Id">@office.Name</option>
                                        }
                                    }
                                </select>
                                <span asp-validation-for="OfficeId" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Description" class="form-label">وصف العرض *</label>
                            <textarea asp-for="Description" class="form-control" rows="4"></textarea>
                            <span asp-validation-for="Description" class="text-danger"></span>
                        </div>

                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label asp-for="Coverage" class="form-label">نوع التغطية</label>
                                <select asp-for="Coverage" class="form-select">
                                    <option value="">اختر نوع التغطية</option>
                                    <option value="شامل">شامل</option>
                                    <option value="ضد الغير">ضد الغير</option>
                                    <option value="ضد الحريق والسرقة">ضد الحريق والسرقة</option>
                                    <option value="تأمين جزئي">تأمين جزئي</option>
                                </select>
                                <span asp-validation-for="Coverage" class="text-danger"></span>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label asp-for="MinPrice" class="form-label">السعر الأدنى</label>
                                <input asp-for="MinPrice" type="number" step="0.01" class="form-control" />
                                <span asp-validation-for="MinPrice" class="text-danger"></span>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label asp-for="MaxPrice" class="form-label">السعر الأعلى</label>
                                <input asp-for="MaxPrice" type="number" step="0.01" class="form-control" />
                                <span asp-validation-for="MaxPrice" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="Currency" class="form-label">العملة</label>
                                <select asp-for="Currency" class="form-select">
                                    <option value="ريال سعودي">ريال سعودي</option>
                                    <option value="دولار أمريكي">دولار أمريكي</option>
                                    <option value="يورو">يورو</option>
                                </select>
                                <span asp-validation-for="Currency" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-check mt-4">
                                    <input asp-for="IsActive" class="form-check-input" checked />
                                    <label asp-for="IsActive" class="form-check-label">
                                        العرض نشط
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Terms" class="form-label">الشروط والأحكام</label>
                            <textarea asp-for="Terms" class="form-control" rows="3" 
                                      placeholder="اكتب الشروط والأحكام الخاصة بهذا العرض..."></textarea>
                            <span asp-validation-for="Terms" class="text-danger"></span>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="@Url.Action("Offers")" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> العودة
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save"></i> حفظ العرض
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
