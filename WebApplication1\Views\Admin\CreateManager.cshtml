@model WebApplication1.ViewModels.CreateManagerViewModel
@{
    ViewData["Title"] = "إنشاء مدير شركة";
    var company = ViewBag.Company as WebApplication1.Models.InsuranceCompany;
}

<div class="container">
    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-user-plus"></i> إنشاء مدير لشركة @company?.Name
                    </h4>
                </div>
                <div class="card-body">
                    @if (company != null)
                    {
                        <div class="alert alert-info mb-4">
                            <i class="fas fa-info-circle"></i>
                            <strong>الشركة:</strong> @company.Name
                            @if (!string.IsNullOrEmpty(company.Description))
                            {
                                <br><small>@company.Description</small>
                            }
                        </div>
                    }

                    <form asp-action="CreateManager" method="post">
                        <input asp-for="CompanyId" type="hidden" />
                        
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="FullName" class="form-label">الاسم الكامل *</label>
                                <input asp-for="FullName" class="form-control" />
                                <span asp-validation-for="FullName" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="Email" class="form-label">البريد الإلكتروني *</label>
                                <input asp-for="Email" type="email" class="form-control" />
                                <span asp-validation-for="Email" class="text-danger"></span>
                                <small class="form-text text-muted">سيتم استخدام البريد الإلكتروني كاسم المستخدم</small>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="Password" class="form-label">كلمة المرور *</label>
                                <input asp-for="Password" type="password" class="form-control" />
                                <span asp-validation-for="Password" class="text-danger"></span>
                                <small class="form-text text-muted">يجب أن تكون على الأقل 6 أحرف</small>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="ConfirmPassword" class="form-label">تأكيد كلمة المرور *</label>
                                <input asp-for="ConfirmPassword" type="password" class="form-control" />
                                <span asp-validation-for="ConfirmPassword" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>ملاحظة:</strong> سيتم منح هذا المستخدم صلاحيات إدارة الشركة بما في ذلك:
                            <ul class="mb-0 mt-2">
                                <li>إدارة مكاتب الشركة</li>
                                <li>إضافة وتعديل العروض</li>
                                <li>عرض إحصائيات الشركة</li>
                            </ul>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="@Url.Action("Companies")" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> العودة
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-user-plus"></i> إنشاء المدير
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
